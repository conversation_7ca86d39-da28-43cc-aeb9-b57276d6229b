package registry

import (
	"chongli/component"
	"chongli/internal/app/task/processors"

	"github.com/robfig/cron/v3"
)

type PicTask struct {
	bootstrap *component.BootStrap
	pic       *processors.PicTaskProcessors
	dance     *processors.DanceTaskProcessors
	avatar    *processors.AvatarPictureProcessors
}

func NewPicTask(
	c *cron.Cron,
	bootstrap *component.BootStrap,
	p *processors.PicTaskProcessors,
	d *processors.DanceTaskProcessors,
	a *processors.AvatarPictureProcessors,
) *PicTask {
	picTask := &PicTask{
		bootstrap: bootstrap,
		pic:       p,
		dance:     d,
		avatar:    a,
	}

	_, _ = c.AddFunc("@every 11s", picTask.pic.AiCompound)
	_, _ = c.AddFunc("@every 12s", picTask.pic.ChangeStyle)
	_, _ = c.AddFunc("@every 13s", picTask.pic.ChangeFace)

	_, _ = c.AddFunc("@every 15s", picTask.dance.PushTask)
	_, _ = c.AddFunc("@every 16s", picTask.dance.QueryResult)

	// 对口型相关的任务
	// 创建角色
	_, _ = c.AddFunc("@every 17s", picTask.avatar.PushRoleTask)
	// 获取角色，发起任务
	_, _ = c.AddFunc("@every 18s", picTask.avatar.GetRoleTask)
	// 获取任务结果
	_, _ = c.AddFunc("@every 19s", picTask.avatar.GetAvatarPictureTask)
	return picTask
}
