package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/pkg/volcengine"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"context"
	"encoding/json"
	"fmt"
	"time"
)

type AvatarPictureProcessors struct {
	bootstrap       *component.BootStrap
	taskService     *service.TaskService
	userWorkService *service.UserWorkService
	log             *logger.Logger
}

func NewAvatarPictureProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
	userWorkService *service.UserWorkService,
) *AvatarPictureProcessors {
	return &AvatarPictureProcessors{
		bootstrap:       bootstrap,
		taskService:     taskService,
		userWorkService: userWorkService,
		log:             bootstrap.Log,
	}
}

func (s *AvatarPictureProcessors) PushRoleTask() {
	ctx := context.Background()

	step, err := s.taskService.GetPendingTaskStep(ctx, model.StepNameCreateRole, nil)
	if err != nil {
		s.log.Error("获取待处理的步骤失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有待处理的步骤：%v", model.StepNameCreateRole)
		return
	}

	// 验证参数
	params := step.Params
	petPic, ok := params["pet_pic"].(string)
	if !ok || petPic == "" {
		s.log.Error("步骤ID: %d - pet_pic参数缺失或格式错误", step.ID)
		s.handleStepError(ctx, step, "pet_pic参数缺失或格式错误")
		return
	}

	// 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 构建角色创建任务请求
	volcReq := volcengine.NewSubmitTaskRequest(map[string]interface{}{
		"req_key":   volcengine.RealmanAvatarPictureCreateRoleLoopy,
		"image_url": petPic,
	})

	// 提交任务
	volcResp, code, err := volcClient.CVSubmitTask(volcReq)
	if code == 429 {
		s.log.Error("步骤ID: %d - 超过频率限制请等待: %d", step.ID, code)
		return
	}
	if err != nil {
		s.log.Error("步骤ID: %d - 提交火山引擎角色创建任务失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("提交火山引擎角色创建任务失败: %v", err))
		return
	}

	if volcResp.Error != "" {
		s.log.Error("步骤ID: %d - 角色创建任务返回错误: %s", step.ID, volcResp.Error)
		s.handleStepError(ctx, step, fmt.Sprintf("角色创建任务返回错误: %s", volcResp.Error))
		return
	}

	// 将task_id写入result并更新状态为waiting_result
	resultJSON, err := json.Marshal(map[string]interface{}{
		"task_id": volcResp.Data.TaskID,
	})
	if err != nil {
		s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("序列化结果数据失败: %v", err))
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("更新步骤状态失败: %v", err))
		return
	}

	s.log.Info("步骤ID: %d - 角色创建任务已提交，task_id: %s", step.ID, volcResp.Data.TaskID)
}

func (s *AvatarPictureProcessors) GetRoleTask() {
	ctx := context.Background()

	// 1. 获取状态为waiting_result，步骤名为create_role的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameCreateRole)
	if err != nil {
		s.log.Error("获取waiting_result状态的create_role任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的create_role任务")
		return
	}

	s.log.Info("步骤ID: %d - 开始处理角色创建任务结果获取", step.ID)

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.log.Error("步骤ID: %d - 解析task_id失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("解析task_id失败: %v", err))
		return
	}

	// 3. 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 4. 调用CVSync2AsyncGetResult获取结果
	getResultReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: volcengine.RealmanAvatarPictureCreateRoleLoopy,
		TaskID: taskID,
	}

	volcResp, err := volcClient.CVGetResult(getResultReq)
	if err != nil {
		s.log.Error("步骤ID: %d - 获取火山引擎角色创建任务结果失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("获取火山引擎角色创建任务结果失败: %v", err))
		s.log.Info("步骤ID: %d - 角色创建任务 %s 仍在处理中，继续等待", step.ID, taskID)
		return
	}
	if volcResp != nil && volcResp.Data.RespData != "" {

		// 6. 解析RespData JSON字符串获取resource_id
		var respData map[string]any
		if err := json.Unmarshal([]byte(volcResp.Data.RespData), &respData); err != nil {
			s.log.Error("步骤ID: %d - 解析RespData失败: %v", step.ID, err)
			s.handleStepError(ctx, step, fmt.Sprintf("解析RespData失败: %v", err))
			return
		}

		resourceID, ok := respData["resource_id"].(string)
		if !ok || resourceID == "" {
			s.log.Error("步骤ID: %d - 未能从RespData中获取到resource_id", step.ID)
			s.handleStepError(ctx, step, "未能从RespData中获取到resource_id")
			return
		}

		// 获取下一步
		nextSteps, err := s.taskService.GetStepQuery(ctx, &dto.GetStepQuery{
			Step: &dto.TaskStepDTO{
				StepIndex: step.StepIndex + 1,
				WorkID:    step.WorkID,
			},
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 获取下一步失败: %v", step.ID, err)
			s.handleStepError(ctx, step, fmt.Sprintf("获取下一步失败: %v", err))
			return
		}
		if nextSteps == nil {
			s.log.Info("步骤ID: %d - 任务 %d 已完成", step.ID, step.WorkID)
			return
		}
		if len(nextSteps) == 0 {
			s.log.Error("步骤ID: %d - 获取下一步失败: 获取到的下一步是0个", step.ID)
			return
		}
		nextStep := nextSteps[0]
		// 检查下一步参数
		if nextStep.Params == nil {
			s.log.Error("步骤ID: %d - 下一步参数为空", nextStep.ID)
			s.handleStepError(ctx, nextStep, "下一步参数为空")
			return
		}
		audioURL, ok := nextStep.Params["audio_url"]
		if !ok {
			s.log.Error("步骤ID: %d - 下一步参数中缺少audio_url", nextStep.ID)
			s.handleStepError(ctx, nextStep, "下一步参数中缺少audio_url")
			return
		}
		audioURLStr, ok := audioURL.(string)
		if !ok {
			s.log.Error("步骤ID: %d - audio_url参数类型错误，应为string", nextStep.ID)
			s.handleStepError(ctx, nextStep, "audio_url参数类型错误，应为string")
			return
		}
		avatarResp, code, err := volcClient.CVSubmitTask(&volcengine.CVSync2AsyncSubmitTaskRequest{
			ReqBody: map[string]any{
				"req_key":     volcengine.RealmanAvatarPictureLoopy,
				"resource_id": resourceID,
				"audio_url":   audioURLStr,
			},
		})
		if code == 429 {
			s.log.Error("步骤ID: %d - 超过频率限制请等待: %d", nextStep.ID, code)
			return
		}
		if err != nil {
			s.log.Error("步骤ID: %d - 生成对口型失败: %v", nextStep.ID, err)
			s.handleStepError(ctx, nextStep, fmt.Sprintf("生成对口型失败: %v", err))
			return
		}
		if code != 200 {
			s.log.Error("步骤ID: %d - 生成对口型失败: %d", nextStep.ID, code)
			s.handleStepError(ctx, nextStep, fmt.Sprintf("生成对口型失败: %d", code))
			return
		}
		if avatarResp.Error != "" {
			s.log.Error("步骤ID: %d - 生成对口型失败: %s", nextStep.ID, avatarResp.Error)
			s.handleStepError(ctx, nextStep, fmt.Sprintf("生成对口型失败: %s", avatarResp.Error))
			return
		}
		// 将上一步任务完成
		resultJSON, _ := json.Marshal(map[string]any{
			"task_id":     taskID,
			"resource_id": resourceID,
		})
		err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
		if err != nil {
			s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
			s.handleStepError(ctx, step, fmt.Sprintf("更新步骤状态失败: %v", err))
			return
		}
		// 将task_id写入result并更新状态为waiting_result
		avatarResultJSON, err := json.Marshal(map[string]any{
			"task_id": avatarResp.Data.TaskID,
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", nextStep.ID, err)
			s.handleStepError(ctx, nextStep, fmt.Sprintf("序列化结果数据失败: %v", err))
			return
		}
		err = s.taskService.UpdateStepStatusAndResult(ctx, nextStep, model.StepStatusWaitingResult, avatarResultJSON)
		if err != nil {
			s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", nextStep.ID, err)
			s.handleStepError(ctx, nextStep, fmt.Sprintf("更新步骤状态失败: %v", err))
			return
		}
		s.log.Info("步骤ID: %d - 视频生成任务已提交，task_id: %s", nextStep.ID, avatarResp.Data.TaskID)
	} else {
		s.log.Info("步骤ID: %d - 视频生成任务 %s 仍在处理中，继续等待", step.ID, taskID)
	}
}

func (s *AvatarPictureProcessors) GetAvatarPictureTask() {
	ctx := context.Background()

	// 1. 获取状态为waiting_result，步骤名为create_role的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameAvatarPicture)
	if err != nil {
		s.log.Error("获取waiting_result状态的create_role任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的create_role任务")
		return
	}

	s.log.Info("步骤ID: %d - 开始处理角色创建任务结果获取", step.ID)

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.log.Error("步骤ID: %d - 解析task_id失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("解析task_id失败: %v", err))
		return
	}

	// 3. 创建火山引擎客户端
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})

	// 4. 调用CVSync2AsyncGetResult获取结果
	getResultReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: volcengine.RealmanAvatarPictureCreateRoleLoopy,
		TaskID: taskID,
	}

	volcResp, err := volcClient.CVGetResult(getResultReq)
	if err != nil {
		s.log.Error("步骤ID: %d - 获取火山引擎角色创建任务结果失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("获取火山引擎角色创建任务结果失败: %v", err))
		return
	}

	if volcResp == nil {
		s.log.Info("步骤ID: %d - 生成对口型视频 %s 仍在处理中，继续等待", step.ID, taskID)
		return
	}

	if volcResp.Error != "" {
		s.log.Error("步骤ID: %d - 生成对口型视频 %s 返回错误: %s", step.ID, taskID, volcResp.Error)
		s.handleStepError(ctx, step, fmt.Sprintf("生成对口型视频 %s 返回错误: %s", taskID, volcResp.Error))
		return
	}

	// 6. 解析RespData JSON字符串获取结果
	var respData map[string]any
	if err := json.Unmarshal([]byte(volcResp.Data.RespData), &respData); err != nil {
		s.log.Error("步骤ID: %d - 解析RespData失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("解析RespData失败: %v", err))
		return
	}

	// 7. 检查code是否为0（成功）
	code, ok := respData["code"].(float64)
	if !ok {
		s.log.Error("步骤ID: %d - 未能从RespData中获取到code字段", step.ID)
		s.handleStepError(ctx, step, "未能从RespData中获取到code字段")
		return
	}

	if code != 0 {
		msg, _ := respData["msg"].(string)
		s.log.Error("步骤ID: %d - 视频生成任务失败，code: %.0f, msg: %s", step.ID, code, msg)
		s.handleStepError(ctx, step, fmt.Sprintf("视频生成任务失败，code: %.0f, msg: %s", code, msg))
		return
	}

	// 8. 获取preview_url数组的第一个值
	previewURLs, ok := respData["preview_url"].([]interface{})
	if !ok || len(previewURLs) == 0 {
		s.log.Error("步骤ID: %d - 未能从RespData中获取到preview_url或数组为空", step.ID)
		s.handleStepError(ctx, step, "未能从RespData中获取到preview_url或数组为空")
		return
	}

	previewURL, ok := previewURLs[0].(string)
	if !ok || previewURL == "" {
		s.log.Error("步骤ID: %d - preview_url的第一个值不是有效的字符串", step.ID)
		s.handleStepError(ctx, step, "preview_url的第一个值不是有效的字符串")
		return
	}

	// 9. 更新步骤状态为完成，并将preview_url存入result
	resultJSON, err := json.Marshal(map[string]any{
		"task_id":     taskID,
		"preview_url": previewURL,
		"code":        code,
		"msg":         respData["msg"],
	})
	if err != nil {
		s.log.Error("步骤ID: %d - 序列化结果数据失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("序列化结果数据失败: %v", err))
		return
	}

	// 将视频文件从预览URL抓取到七牛云存储
	// 生成文件名（使用步骤ID和task_id）
	filename := fmt.Sprintf("avatar_video_%d_%s.mp4", step.ID, taskID)
	// 按照指定规则生成目标文件地址
	qiniuPath := fmt.Sprintf("%s/%s", time.Now().Format("2006/01/02"), filename)
	qiniuURL, err := qiniu.FetchFile(previewURL, qiniuPath)
	if err != nil {
		s.log.Error("步骤ID: %d - 抓取视频文件到七牛云失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("视频转存七牛失败: %v", err))
		// 这里不直接返回错误，而是使用原始URL
		qiniuURL = previewURL
	} else {
		s.log.Info("步骤ID: %d - 视频文件已抓取到七牛云: %s", step.ID, qiniuURL)
	}
	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
	if err != nil {
		s.log.Error("步骤ID: %d - 更新步骤状态失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("更新步骤状态失败: %v", err))
		return
	}
	// err = s.taskService.CompleteTask(ctx, &step.Task, "", nil)
	// if err != nil {
	// 	s.log.Error("步骤ID: %d - 完成任务失败: %v", step.ID, err)
	// 	s.handleStepError(ctx, step, fmt.Sprintf("完成任务失败: %v", err))
	// 	return
	// }

	err = s.userWorkService.DoneUserWork(ctx, uint64(step.WorkID), map[string]any{
		"video_url": qiniuURL,
	}, nil)
	if err != nil {
		s.log.Error("步骤ID: %d - 完成用户作品失败: %v", step.ID, err)
		s.handleStepError(ctx, step, fmt.Sprintf("完成用户作品失败: %v", err))
		return
	}
	s.log.Info("步骤ID: %d - 视频生成任务完成，preview_url: %s, qiniu_url: %s", step.ID, previewURL, qiniuURL)

}

// getWaitingResultStep 获取状态为waiting_result且步骤名为指定名称的任务步骤
func (s *AvatarPictureProcessors) getWaitingResultStep(ctx context.Context, stepName string) (*dto.TaskStepDTO, error) {
	limit := 1
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusWaitingResult,
			StepName: stepName,
		},
		Limit: &limit,
	}

	stepDTOs, err := s.taskService.GetStepQuery(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("获取waiting_result状态的步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有找到对应的步骤
	}

	return stepDTOs[0], nil
}

// extractTaskIDFromResult 从result字段中解析task_id
func (s *AvatarPictureProcessors) extractTaskIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	taskID, ok := result["task_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到task_id或类型错误")
	}

	if taskID == "" {
		return "", fmt.Errorf("task_id为空")
	}

	return taskID, nil
}

// handleStepError 统一处理步骤失败的错误
func (s *AvatarPictureProcessors) handleStepError(ctx context.Context, step *dto.TaskStepDTO, errorMsg string) {
	s.log.Error("步骤ID: %d - %s", step.ID, errorMsg)
	step.Status = model.StepStatusFailed
	step.ErrorMsg = errorMsg
	err := s.taskService.StepFail(ctx, step)
	if err != nil {
		s.log.Error("步骤ID: %d - 标记步骤失败时出错: %v", step.ID, err)
	}
}
