package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type UserWorkRouter struct {
	userWorkCtrl *controller.UserWorkController
}

// NewUserWorkRouter 创建用户作品路由
func NewUserWorkRouter(engine *gin.Engine, userWorkCtrl *controller.UserWorkController) *UserWorkRouter {
	router := &UserWorkRouter{userWorkCtrl: userWorkCtrl}

	// 用户作品相关路由（需要JWT验证）
	userWork := engine.Group("/admin/user_work")
	userWork.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		userWork.GET("list", router.userWorkCtrl.UserWorkList)     // 获取用户作品列表
		userWork.GET("delete", router.userWorkCtrl.DeleteUserWork) // 删除用户作品
		userWork.POST("edit", router.userWorkCtrl.UpdateUserWork)  // 编辑用户作品信息
	}
	return router
}
