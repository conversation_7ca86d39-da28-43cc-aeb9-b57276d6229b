import os
import json
import types
import time
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.vclm.v20240523 import vclm_client, models


def submit_portrait_sing_job(audio_url, image_url, secret_id=None, secret_key=None):
    """
    提交人像唱歌任务
    
    Args:
        audio_url (str): 音频文件URL
        image_url (str): 图片文件URL
        secret_id (str, optional): 腾讯云SecretId，如果不提供则使用环境变量
        secret_key (str, optional): 腾讯云SecretKey，如果不提供则使用环境变量
    
    Returns:
        str: 任务提交结果的JSON字符串，如果出错则返回None
    """
    try:
        # 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        # 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性
        # 以下代码示例仅供参考，建议采用更安全的方式来使用密钥
        # 请参见：https://cloud.tencent.com/document/product/1278/85305
        # 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
        if secret_id and secret_key:
            cred = credential.Credential(secret_id, secret_key)
        else:
            cred = credential.Credential(os.getenv("TENCENTCLOUD_SECRET_ID"), os.getenv("TENCENTCLOUD_SECRET_KEY"))
        
        # 实例化一个http选项，可选的，没有特殊需求可以跳过
        httpProfile = HttpProfile()
        httpProfile.endpoint = "vclm.tencentcloudapi.com"

        # 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        # 实例化要请求产品的client对象,clientProfile是可选的
        client = vclm_client.VclmClient(cred, "ap-guangzhou", clientProfile)

        # 实例化一个请求对象,每个接口都会对应一个request对象
        req = models.SubmitPortraitSingJobRequest()
        params = {
            "AudioUrl": audio_url,
            "ImageUrl": image_url,
            "Mode": "Pet", 
        }
        req.from_json_string(json.dumps(params))

        # 返回的resp是一个SubmitPortraitSingJobResponse的实例，与请求对象对应
        resp = client.SubmitPortraitSingJob(req)
        # 输出json格式的字符串回包
        result = resp.to_json_string()
        print(result)
        return result

    except TencentCloudSDKException as err:
        print(f"提交任务失败: {err}")
        return None


def describe_portrait_sing_job(job_id, secret_id=None, secret_key=None):
    """
    查询人像唱歌任务状态
    
    Args:
        job_id (str): 任务ID
        secret_id (str, optional): 腾讯云SecretId，如果不提供则使用环境变量
        secret_key (str, optional): 腾讯云SecretKey，如果不提供则使用环境变量
    
    Returns:
        str: 任务状态查询结果的JSON字符串，如果出错则返回None
    """
    try:
        # 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        # 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性
        # 以下代码示例仅供参考，建议采用更安全的方式来使用密钥
        # 请参见：https://cloud.tencent.com/document/product/1278/85305
        # 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
        if secret_id and secret_key:
            cred = credential.Credential(secret_id, secret_key)
        else:
            cred = credential.Credential(os.getenv("TENCENTCLOUD_SECRET_ID"), os.getenv("TENCENTCLOUD_SECRET_KEY"))
        
        # 实例化一个http选项，可选的，没有特殊需求可以跳过
        httpProfile = HttpProfile()
        httpProfile.endpoint = "vclm.tencentcloudapi.com"

        # 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        # 实例化要请求产品的client对象,clientProfile是可选的
        client = vclm_client.VclmClient(cred, "ap-guangzhou", clientProfile)

        # 实例化一个请求对象,每个接口都会对应一个request对象
        req = models.DescribePortraitSingJobRequest()
        params = {
            "JobId": job_id
        }
        req.from_json_string(json.dumps(params))

        # 返回的resp是一个DescribePortraitSingJobResponse的实例，与请求对象对应
        resp = client.DescribePortraitSingJob(req)
        # 输出json格式的字符串回包
        result = resp.to_json_string()
        print(result)
        return result

    except TencentCloudSDKException as err:
        print(f"查询任务失败: {err}")
        return None


def wait_for_job_completion(job_id, secret_id=None, secret_key=None, check_interval=5):
    """
    循环查询人像唱歌任务状态直到完成
    
    Args:
        job_id (str): 任务ID
        secret_id (str, optional): 腾讯云SecretId，如果不提供则使用环境变量
        secret_key (str, optional): 腾讯云SecretKey，如果不提供则使用环境变量
        check_interval (int): 查询间隔时间（秒），默认5秒
    
    Returns:
        dict: 最终的任务结果，如果失败则返回None
    """
    print(f"开始查询任务状态，任务ID: {job_id}")
    
    while True:
        describe_result = describe_portrait_sing_job(job_id, secret_id, secret_key)
        
        if describe_result:
            result_data = json.loads(describe_result)
            status_code = result_data.get('StatusCode', '')
            status_msg = result_data.get('StatusMsg', '')
            
            print(f"当前状态: {status_code} - {status_msg}")
            
            if status_code == "DONE":
                print("任务处理完成！")
                print(f"结果视频URL: {result_data.get('ResultVideoUrl', '')}")
                print(f"完整结果: {describe_result}")
                return result_data
            elif status_code == "FAILED":
                print("任务处理失败！")
                print(f"错误信息: {result_data.get('ErrorMessage', '')}")
                return None
            elif status_code == "FAIL":
                print("任务处理失败！")
                print(f"错误信息: {result_data.get('ErrorMessage', '')}")
                return None
            else:
                print(f"任务还在处理中，{check_interval}秒后再次查询...")
                time.sleep(check_interval)
        else:
            print(f"查询失败，{check_interval}秒后重试...")
            time.sleep(check_interval)


if __name__ == "__main__":
    # 密钥配置
    secret_id = "AKIDLYYKvJSWxCF6kDVZUM0SvCJEyZ1HKBsT"
    secret_key = "fBbAc1OYVGpBJDvRjnfKl16zhCWeaJp7"
    
    # 示例用法
    # 提交任务
    # audio_url = "https://chongli-cdn.51wnl-cq.com/test/haokan.MP3"
    # image_url = "https://chongli-cdn.51wnl-cq.com/test/dog.jpg"
    # submit_result = submit_portrait_sing_job(audio_url, image_url, secret_id, secret_key)
    
    # 查询任务状态
    job_id = "1344621569238155264"
    
    # 使用循环查询函数
    wait_for_job_completion(job_id, secret_id, secret_key)